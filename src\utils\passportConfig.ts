import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import { User } from 'entities'; // Assuming User entity exists in your application

// Define a type for the Google profile
interface GoogleProfile {
  id: string;
  displayName: string;
  emails?: { value: string }[];
  photos?: { value: string }[]; // Add photos to the interface
}

// Configure the Google Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID || '',
      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
      callbackURL: 'http://localhost:3000/auth/google/callback',
      scope: ['profile', 'email'],
    },
    (_accessToken, _refreshToken, profile: GoogleProfile, done) => {
      // Perform asynchronous operations
      User.findOne({ where: { googleLogin: profile.id } })
        .then(async existingUser => {
          if (!existingUser) {
            const email =
              profile.emails && profile.emails.length > 0 ? profile.emails[0].value : null;
            const avatarUrl =
              profile.photos && profile.photos.length > 0 ? profile.photos[0].value : null;

            if (!email) {
              return done(new Error('No email found for Google account.'));
            }

            const newUser = User.create({
              googleLogin: profile.id,
              email,
              name: profile.displayName,
              avatarUrl: avatarUrl || undefined,
            });

            await newUser.save();
            return done(null, newUser);
          }

          return done(null, existingUser);
        })
        .catch(error => {
          console.error('Error during Google authentication:', error);
          return done(error);
        });
    },
  ),
);

// Serialize user to the session
passport.serializeUser((user: any, done) => {
  done(null, user.id);
});

// Deserialize user from the session
passport.deserializeUser((id: string, done) => {
  const userId = parseInt(id, 10);
  User.findOne({ where: { id: userId } })
    .then(user => done(null, user))
    .catch(error => {
      console.error('Error deserializing user:', error);
      done(error);
    });
});

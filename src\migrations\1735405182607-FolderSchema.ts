import { MigrationInterface, QueryRunner } from "typeorm";

export class FolderSchema1735405182607 implements MigrationInterface {
    name = 'FolderSchema1735405182607'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "folder" ("id" SERIAL NOT NULL, "createdBy" integer NOT NULL, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "lastAccessed" TIMESTAMP WITH TIME ZONE, "name" text NOT NULL, "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "path" text NOT NULL, "pathNames" text, "parent" integer, CONSTRAINT "PK_6278a41a706740c94c02e288df8" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "folder"`);
    }

}

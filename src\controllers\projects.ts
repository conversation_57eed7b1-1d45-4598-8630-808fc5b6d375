import { Project, User, UserEntity } from 'entities';
import { catchErrors } from 'errors';
import { createEntity, findEntityOrThrow, updateEntity } from 'utils/typeorm';
import { issuePartial } from 'serializers/issues';

export const getProjectWithUsersAndIssues = catchErrors(async (_req, res) => {
  const project = await findEntityOrThrow(Project, 1, {
    relations: ['issues'],
  });
  res.respond({
    project: {
      ...project,
      issues: project.issues.map(issuePartial),
    },
  });
});

export const getProjects = catchErrors(async (_, res) => {
  const projects = await Project.find({
    relations: ['issues'],
  });

  res.respond({
    projects,
  });
});

export const getProject = catchErrors(async (req, res) => {
  const { projectId } = req.params;
  const project = await Project.findOne({
    where: { id: Number(projectId) },
    relations: ['issues'],
  });
  const rawUsers = await UserEntity.createQueryBuilder('user_entity')
    .leftJoinAndSelect('user_entity.user', 'user')
    .select([
      'user.id AS id',
      'user.name AS name',
      'user.email AS email',
      'user.avatarUrl AS avatarUrl',
      'user.role AS role',
      'user.createdAt AS createdAt',
      'user.updatedAt AS updatedAt',
      'user.lastLogin AS lastLogin',
      'user_entity.accessType AS accessType',
      'user_entity.entityId AS projectId',
    ])
    .where('user_entity.entityType = :entityType AND user_entity.entityId = :entityId', {
      entityType: 'project',
      entityId: projectId, // Replace with actual entity ID
    })
    .getRawMany();
  const users = rawUsers.map(user => ({
    id: user.id,
    name: user.name,
    email: user.email,
    avatarUrl: user.avatarurl,
    role: user.role,
    createdAt: user.createdat,
    updatedAt: user.updatedat,
    lastLogin: user.lastlogin,
    accessType: user.accesstype,
    projectId: user.projectid,
  }));
  res.respond({ ...project, users });
});

export const update = catchErrors(async (req, res) => {
  const project = await updateEntity(Project, 1, req.body);
  res.respond({ project });
});

export const create = catchErrors(async (req, res) => {
  const userId = req.currentUser.id; // Assuming the authenticated user's ID is available in `req.user`

  const project = await createEntity(Project, req.body);

  const user = await User.findOneOrFail({ where: { id: userId } });

  const userProject = await createEntity(UserEntity, {
    user,
    entityId: project.id,
    entityType: 'project',
    role: 'lead',
  });

  await userProject.save();
  res.respond({ project, userProject });
});

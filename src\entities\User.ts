import {
  BaseEntity,
  <PERSON>tity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
} from 'typeorm';

import is from 'utils/validation';
import { Comment, Issue, UserEntity } from '.';

@Entity()
class User extends BaseEntity {
  static validations = {
    name: [is.required(), is.maxLength(100)],
    email: [is.required(), is.email(), is.maxLength(200)],
  };

  @PrimaryGeneratedColumn()
  id: number;

  @Column('varchar')
  name: string;

  @Column('varchar')
  email: string;

  @Column('varchar', { length: 2000 })
  avatarUrl: string;

  @Column('varchar', { nullable: true }) // googleLogin (nullable)
  googleLogin: string | null;

  @Column('varchar', { nullable: true }) // password (nullable)
  password: string | null;

  @Column('varchar', { default: 'user' }) // role (default to 'user')
  role: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp' })
  updatedAt: Date;

  @Column('timestamp', { nullable: true }) // lastLogin (nullable)
  lastLogin: Date | null;

  @OneToMany(
    () => Comment,
    comment => comment.user,
  )
  comments: Comment[];

  @ManyToMany(
    () => Issue,
    issue => issue.users,
  )
  issues: Issue[];

  @OneToMany(
    () => UserEntity,
    userEntity => userEntity.user,
  )
  userEntities: UserEntity[];
}

export default User;

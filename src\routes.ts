import * as comments from 'controllers/comments';
import * as issues from 'controllers/issues';
import * as projects from 'controllers/projects';
import * as users from 'controllers/users';
import * as Folders from 'controllers/folders';

export const attachPublicRoutes = (app: any): void => {
  app.get('/');
};

export const attachPrivateRoutes = (app: any): void => {
  app.post('/comments', comments.create);
  app.put('/comments/:commentId', comments.update);
  app.delete('/comments/:commentId', comments.remove);

  app.get('/issues', issues.getProjectIssues);
  app.get('/issues/:issueId', issues.getIssueWithUsersAndComments);
  app.post('/issues', issues.create);
  app.put('/issues/:issueId', issues.update);
  app.delete('/issues/:issueId', issues.remove);

  app.get('/project', projects.getProjectWithUsersAndIssues);
  app.get('/projects', projects.getProjects);
  app.get('/project/:projectId/board', projects.getProject);
  app.put('/project', projects.update);
  app.post('/project', projects.create);

  app.get('/folders/root', Folders.getRootFolders);
  app.get('/folder/:id', Folders.getFolderDetails);
  app.get('/folders/:parentId', Folders.getSubFolders);
  app.post('/folder', Folders.createFolder);
  app.put('/folder/:id', Folders.updateFolder);
  app.delete('/folder/:id', Folders.deleteFolder);

  app.get('/currentUser', users.getCurrentUser);
};
